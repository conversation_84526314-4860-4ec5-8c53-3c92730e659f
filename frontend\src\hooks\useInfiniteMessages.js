import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback, useEffect, useRef, useMemo } from 'react';
import { chatApi } from '../api/chatApi';
import { useSocket } from '../contexts/SocketContext';

export const useInfiniteMessages = (chatId, options = {}) => {
  const queryClient = useQueryClient();
  const { addEventListener } = useSocket();
  const scrollPositionRef = useRef(new Map()); // Store scroll positions per chat
  
  const {
    enabled = true,
    staleTime = 5 * 60 * 1000, // 5 minutes
    cacheTime = 10 * 60 * 1000, // 10 minutes
    limit = 50,
  } = options;

  // Query key for this chat's messages - memoized to prevent re-creation
  const queryKey = useMemo(() => ['messages', chatId], [chatId]);

  // Infinite query for messages
  const {
    data,
    error,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isFetchingNextPage,
    isLoading,
    isError,
    refetch,
  } = useInfiniteQuery({
    queryKey,
    queryFn: async ({ pageParam = null }) => {
      if (!chatId) throw new Error('Chat ID is required');
      
      const response = await chatApi.getChatMessages(chatId, limit, pageParam);
      return response;
    },
    getNextPageParam: (lastPage) => {
      // Return the cursor for the next page (oldest message ID)
      if (lastPage.pagination.hasMore && lastPage.messages.length > 0) {
        return lastPage.messages[0]._id; // First message is the oldest
      }
      return undefined;
    },
    enabled: enabled && !!chatId,
    staleTime,
    cacheTime,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  // Flatten all messages from all pages
  const messages = data?.pages?.flatMap(page => page.messages) || [];
  
  // Get total message count
  const totalMessages = messages.length;

  // Real-time message handling with stable event handlers
  useEffect(() => {
    if (!chatId || !addEventListener) return;

    // Store cleanup functions
    const cleanupFunctions = [];

    // Define handlers inside useEffect to avoid circular dependencies
    const handleNewMessage = (data) => {
      if (data.chatId !== chatId) return;

      queryClient.setQueryData(queryKey, (oldData) => {
        if (!oldData) return oldData;

        // Check if message already exists (avoid duplicates)
        const messageExists = oldData.pages.some(page =>
          page.messages.some(msg => msg._id === data.message._id)
        );

        if (messageExists) return oldData;

        // Add new message to the last page (most recent)
        // This handler only receives messages from OTHER users now
        const newPages = [...oldData.pages];
        if (newPages.length > 0) {
          const lastPage = { ...newPages[newPages.length - 1] };
          lastPage.messages = [...lastPage.messages, data.message];
          newPages[newPages.length - 1] = lastPage;
        } else {
          // Create first page if no pages exist
          newPages.push({
            messages: [data.message],
            pagination: { hasMore: false, nextCursor: null, limit: 50 }
          });
        }

        return {
          ...oldData,
          pages: newPages,
        };
      });
    };

    // Handle message sent confirmation (for own messages)
    const handleMessageSent = (data) => {
      if (data.chatId !== chatId) return;

      queryClient.setQueryData(queryKey, (oldData) => {
        if (!oldData) return oldData;

        // Remove optimistic message and add real message
        const newPages = oldData.pages.map(page => ({
          ...page,
          messages: page.messages.map(msg => {
            // Replace optimistic message with real message
            if (msg.isOptimistic &&
                msg.senderId === data.message.senderId._id &&
                msg.text?.trim() === data.message.text?.trim()) {
              return data.message; // Replace with real message
            }
            return msg;
          })
        }));

        return {
          ...oldData,
          pages: newPages,
        };
      });
    };

    const handleMessageDelivered = (data) => {
      if (data.chatId !== chatId) return;

      queryClient.setQueryData(queryKey, (oldData) => {
        if (!oldData) return oldData;

        const newPages = oldData.pages.map(page => ({
          ...page,
          messages: page.messages.map(msg => {
            if (msg._id === data.messageId) {
              return {
                ...msg,
                status: {
                  ...msg.status,
                  delivered: [
                    ...msg.status.delivered,
                    ...data.deliveredTo.map(userId => ({
                      userId,
                      timestamp: data.timestamp
                    }))
                  ]
                }
              };
            }
            return msg;
          })
        }));

        return { ...oldData, pages: newPages };
      });
    };

    const handleMessageSeen = (data) => {
      if (data.chatId !== chatId) return;

      queryClient.setQueryData(queryKey, (oldData) => {
        if (!oldData) return oldData;

        const newPages = oldData.pages.map(page => ({
          ...page,
          messages: page.messages.map(msg => {
            if (data.messageIds.includes(msg._id)) {
              const existingSeen = msg.status.seen || [];
              const alreadySeen = existingSeen.some(s => s.userId === data.seenBy);

              if (!alreadySeen) {
                return {
                  ...msg,
                  status: {
                    ...msg.status,
                    seen: [
                      ...existingSeen,
                      {
                        userId: data.seenBy,
                        timestamp: data.timestamp
                      }
                    ]
                  }
                };
              }
            }
            return msg;
          })
        }));

        return { ...oldData, pages: newPages };
      });
    };

    // Add event listeners - handle both sync and async returns
    const setupEventListeners = async () => {
      try {
        const cleanupNewMessage = await addEventListener('message:new', handleNewMessage);
        const cleanupMessageSent = await addEventListener('message:sent', handleMessageSent);
        const cleanupDelivered = await addEventListener('message:delivered', handleMessageDelivered);
        const cleanupSeen = await addEventListener('message:seen', handleMessageSeen);

        cleanupFunctions.push(cleanupNewMessage, cleanupMessageSent, cleanupDelivered, cleanupSeen);
      } catch (error) {
        console.error('Failed to setup event listeners:', error);
      }
    };

    setupEventListeners();

    return () => {
      cleanupFunctions.forEach(cleanup => {
        if (typeof cleanup === 'function') {
          cleanup();
        }
      });
    };
  }, [chatId, addEventListener, queryKey, queryClient]); // Remove limit from dependencies

  // Optimistic message update
  const addOptimisticMessage = useCallback((tempMessage) => {
    queryClient.setQueryData(queryKey, (oldData) => {
      if (!oldData) return oldData;

      const newPages = [...oldData.pages];
      if (newPages.length > 0) {
        const lastPage = { ...newPages[newPages.length - 1] };
        lastPage.messages = [...lastPage.messages, tempMessage];
        newPages[newPages.length - 1] = lastPage;
      } else {
        newPages.push({
          messages: [tempMessage],
          pagination: { hasMore: false, nextCursor: null, limit: 50 }
        });
      }

      return {
        ...oldData,
        pages: newPages,
      };
    });
  }, [queryKey, queryClient]);

  // Remove optimistic message (when real message arrives)
  const removeOptimisticMessage = useCallback((tempId) => {
    queryClient.setQueryData(queryKey, (oldData) => {
      if (!oldData) return oldData;

      const newPages = oldData.pages.map(page => ({
        ...page,
        messages: page.messages.filter(msg => {
          // Remove by tempId for optimistic messages
          if (msg.tempId === tempId) return false;

          // Also remove any orphaned optimistic messages older than 30 seconds
          if (msg.isOptimistic && msg.tempId) {
            const messageAge = Date.now() - new Date(msg.createdAt).getTime();
            if (messageAge > 30000) { // 30 seconds
              console.log('🧹 Removing orphaned optimistic message:', msg.tempId);
              return false;
            }
          }

          return true;
        })
      }));

      return { ...oldData, pages: newPages };
    });
  }, [queryKey, queryClient]);

  // Clean up orphaned optimistic messages periodically
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      queryClient.setQueryData(queryKey, (oldData) => {
        if (!oldData) return oldData;

        const newPages = oldData.pages.map(page => ({
          ...page,
          messages: page.messages.filter(msg => {
            if (!msg.isOptimistic) return true;

            const messageAge = Date.now() - new Date(msg.createdAt).getTime();
            if (messageAge > 30000) { // Remove optimistic messages older than 30 seconds
              console.log('🧹 Cleaning up orphaned optimistic message:', msg.tempId);
              return false;
            }

            return true;
          })
        }));

        return {
          ...oldData,
          pages: newPages,
        };
      });
    }, 10000); // Check every 10 seconds

    return () => clearInterval(cleanupInterval);
  }, [queryKey, queryClient]);

  // Scroll position management
  const saveScrollPosition = useCallback((position) => {
    scrollPositionRef.current.set(chatId, position);
  }, [chatId]);

  const getScrollPosition = useCallback(() => {
    return scrollPositionRef.current.get(chatId) || 0;
  }, [chatId]);

  const clearScrollPosition = useCallback(() => {
    scrollPositionRef.current.delete(chatId);
  }, [chatId]);

  // Load more messages (for infinite scroll)
  const loadMoreMessages = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Invalidate and refetch messages
  const invalidateMessages = useCallback(() => {
    queryClient.invalidateQueries({ queryKey });
  }, [queryClient, queryKey]);

  // Mark messages as seen
  const markMessagesAsSeen = useCallback((messageIds) => {
    // Don't proceed if no message IDs provided
    if (!messageIds || messageIds.length === 0) return;

    // Optimistically update the cache
    queryClient.setQueryData(queryKey, (oldData) => {
      if (!oldData) return oldData;

      const newPages = oldData.pages.map(page => ({
        ...page,
        messages: page.messages.map(msg => {
          if (messageIds.includes(msg._id)) {
            // For now, just mark as seen without specific user tracking
            // This will be handled by the backend socket events
            return {
              ...msg,
              status: {
                ...msg.status,
                seen: [
                  ...(msg.status?.seen || []),
                  {
                    userId: 'current-user', // Placeholder - backend will handle properly
                    timestamp: new Date().toISOString()
                  }
                ]
              }
            };
          }
          return msg;
        })
      }));

      return { ...oldData, pages: newPages };
    });
  }, [queryKey, queryClient]);

  return {
    // Data
    messages,
    totalMessages,
    
    // Loading states
    isLoading,
    isFetching,
    isFetchingNextPage,
    isError,
    error,
    
    // Pagination
    hasNextPage,
    loadMoreMessages,
    fetchNextPage,
    
    // Actions
    refetch,
    invalidateMessages,
    addOptimisticMessage,
    removeOptimisticMessage,
    markMessagesAsSeen,
    
    // Scroll position management
    saveScrollPosition,
    getScrollPosition,
    clearScrollPosition,
    
    // Query info
    queryKey,
  };
};
