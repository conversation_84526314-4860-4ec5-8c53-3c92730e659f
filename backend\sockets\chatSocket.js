const User = require('../models/User');
const Chat = require('../models/Chat');
const Message = require('../models/Message');

// Store active users and their socket connections
const activeUsers = new Map(); // userId -> { socketId, lastSeen }
const typingUsers = new Map(); // chatId -> Set of userIds

const handleChatSocket = (io, socket) => {
  const user = socket.user;
  
  console.log(`🔌 User connected: ${user.name} (${socket.id})`);

  // Store user's socket connection
  activeUsers.set(user._id.toString(), {
    socketId: socket.id,
    lastSeen: new Date(),
    user: user
  });

  // Update user's online status in database
  updateUserOnlineStatus(user._id, true, socket.id);

  // Join user to their personal room for direct notifications
  socket.join(`user:${user._id}`);

  // Join user to all their chat rooms
  joinUserChats(socket, user._id);

  // Emit user online status to all contacts
  broadcastUserStatus(io, user._id, true);

  // ==================== MESSAGE EVENTS ====================

  // Send a new message
  socket.on('message:send', async (data) => {
    try {
      const { chatId, text, replyTo } = data;
      
      // Validate chat membership
      const chat = await Chat.findOne({
        _id: chatId,
        members: user._id
      });

      if (!chat) {
        socket.emit('error', { message: 'Chat not found or access denied' });
        return;
      }

      // Create new message
      const message = new Message({
        chatId,
        senderId: user._id,
        text: text.trim(),
        replyTo: replyTo || null
      });

      await message.save();

      // Populate sender info
      await message.populate('senderId', 'name email avatar');
      if (replyTo) {
        await message.populate('replyTo', 'text senderId');
      }

      // Update chat's last activity and last message
      chat.lastActivity = new Date();
      chat.lastMessage = message._id;
      await chat.save();

      // Emit to all chat members EXCEPT the sender (to avoid duplicate with optimistic message)
      socket.to(`chat:${chatId}`).emit('message:new', {
        message,
        chatId
      });

      // Send confirmation back to sender only
      socket.emit('message:sent', {
        message,
        chatId,
        tempId: null // This will help identify which optimistic message to remove
      });

      // Mark as delivered for online users in the chat
      const onlineMembers = chat.members.filter(memberId => 
        activeUsers.has(memberId.toString()) && 
        memberId.toString() !== user._id.toString()
      );

      if (onlineMembers.length > 0) {
        message.status.delivered = onlineMembers.map(memberId => ({
          userId: memberId,
          timestamp: new Date()
        }));
        await message.save();

        // Emit delivery confirmation
        io.to(`chat:${chatId}`).emit('message:delivered', {
          messageId: message._id,
          deliveredTo: onlineMembers,
          timestamp: new Date()
        });
      }

      console.log(`📨 Message sent in chat ${chatId} by ${user.name}`);

    } catch (error) {
      console.error('Error sending message:', error);
      socket.emit('error', { message: 'Failed to send message' });
    }
  });

  // Mark messages as seen
  socket.on('message:seen', async (data) => {
    try {
      const { chatId, messageIds } = data;

      // Validate chat membership
      const chat = await Chat.findOne({
        _id: chatId,
        members: user._id
      });

      if (!chat) {
        socket.emit('error', { message: 'Chat not found or access denied' });
        return;
      }

      // Update seen status for multiple messages
      const result = await Message.updateMany(
        {
          _id: { $in: messageIds },
          chatId,
          senderId: { $ne: user._id }, // Don't mark own messages as seen
          'status.seen.userId': { $ne: user._id } // Avoid duplicates
        },
        {
          $push: {
            'status.seen': {
              userId: user._id,
              timestamp: new Date()
            }
          }
        }
      );

      if (result.modifiedCount > 0) {
        // Emit seen confirmation to chat
        io.to(`chat:${chatId}`).emit('message:seen', {
          messageIds,
          seenBy: user._id,
          timestamp: new Date(),
          chatId
        });

        console.log(`👁️ ${result.modifiedCount} messages marked as seen by ${user.name} in chat ${chatId}`);
      }

    } catch (error) {
      console.error('Error marking messages as seen:', error);
      socket.emit('error', { message: 'Failed to mark messages as seen' });
    }
  });

  // ==================== TYPING EVENTS ====================

  // User started typing
  socket.on('typing:start', async (data) => {
    try {
      const { chatId } = data;

      // Validate chat membership
      const chat = await Chat.findOne({
        _id: chatId,
        members: user._id
      });

      if (!chat) return;

      // Add user to typing users for this chat
      if (!typingUsers.has(chatId)) {
        typingUsers.set(chatId, new Set());
      }
      typingUsers.get(chatId).add(user._id.toString());

      // Broadcast to other chat members
      socket.to(`chat:${chatId}`).emit('typing:start', {
        chatId,
        userId: user._id,
        userName: user.name
      });

      console.log(`⌨️ ${user.name} started typing in chat ${chatId}`);

    } catch (error) {
      console.error('Error handling typing start:', error);
    }
  });

  // User stopped typing
  socket.on('typing:stop', async (data) => {
    try {
      const { chatId } = data;

      // Remove user from typing users
      if (typingUsers.has(chatId)) {
        typingUsers.get(chatId).delete(user._id.toString());
        
        // Clean up empty sets
        if (typingUsers.get(chatId).size === 0) {
          typingUsers.delete(chatId);
        }
      }

      // Broadcast to other chat members
      socket.to(`chat:${chatId}`).emit('typing:stop', {
        chatId,
        userId: user._id
      });

      console.log(`⌨️ ${user.name} stopped typing in chat ${chatId}`);

    } catch (error) {
      console.error('Error handling typing stop:', error);
    }
  });

  // ==================== CHAT EVENTS ====================

  // Join a specific chat room
  socket.on('chat:join', async (data) => {
    try {
      const { chatId } = data;

      // Validate chat membership
      const chat = await Chat.findOne({
        _id: chatId,
        members: user._id
      });

      if (!chat) {
        socket.emit('error', { message: 'Chat not found or access denied' });
        return;
      }

      socket.join(`chat:${chatId}`);
      console.log(`🏠 ${user.name} joined chat room: ${chatId}`);

      // Emit join confirmation
      socket.emit('chat:joined', { chatId });

    } catch (error) {
      console.error('Error joining chat:', error);
      socket.emit('error', { message: 'Failed to join chat' });
    }
  });

  // Leave a specific chat room
  socket.on('chat:leave', (data) => {
    const { chatId } = data;
    socket.leave(`chat:${chatId}`);
    
    // Stop typing if user was typing
    if (typingUsers.has(chatId)) {
      typingUsers.get(chatId).delete(user._id.toString());
      socket.to(`chat:${chatId}`).emit('typing:stop', {
        chatId,
        userId: user._id
      });
    }

    console.log(`🚪 ${user.name} left chat room: ${chatId}`);
  });

  // ==================== DISCONNECT EVENTS ====================

  socket.on('disconnect', async () => {
    console.log(`🔌 User disconnected: ${user.name} (${socket.id})`);

    // Remove from active users
    activeUsers.delete(user._id.toString());

    // Update user's offline status
    await updateUserOnlineStatus(user._id, false);

    // Clean up typing status
    for (const [chatId, typingSet] of typingUsers.entries()) {
      if (typingSet.has(user._id.toString())) {
        typingSet.delete(user._id.toString());
        io.to(`chat:${chatId}`).emit('typing:stop', {
          chatId,
          userId: user._id
        });
      }
    }

    // Broadcast user offline status
    broadcastUserStatus(io, user._id, false);
  });
};

// ==================== HELPER FUNCTIONS ====================

// Update user's online status in database
const updateUserOnlineStatus = async (userId, isOnline, socketId = null) => {
  try {
    await User.findByIdAndUpdate(userId, {
      isOnline,
      lastSeen: new Date(),
      socketId: isOnline ? socketId : null
    });
  } catch (error) {
    console.error('Error updating user online status:', error);
  }
};

// Join user to all their chat rooms
const joinUserChats = async (socket, userId) => {
  try {
    const userChats = await Chat.find({ members: userId }).select('_id');
    
    userChats.forEach(chat => {
      socket.join(`chat:${chat._id}`);
    });

    console.log(`🏠 User joined ${userChats.length} chat rooms`);
  } catch (error) {
    console.error('Error joining user chats:', error);
  }
};

// Broadcast user online/offline status to contacts
const broadcastUserStatus = async (io, userId, isOnline) => {
  try {
    // Find all chats where this user is a member
    const userChats = await Chat.find({ members: userId }).populate('members', '_id');
    
    // Get all unique contact user IDs
    const contactIds = new Set();
    userChats.forEach(chat => {
      chat.members.forEach(member => {
        if (member._id.toString() !== userId.toString()) {
          contactIds.add(member._id.toString());
        }
      });
    });

    // Emit status to each contact's personal room
    contactIds.forEach(contactId => {
      io.to(`user:${contactId}`).emit('user:status', {
        userId,
        isOnline,
        lastSeen: new Date()
      });
    });

  } catch (error) {
    console.error('Error broadcasting user status:', error);
  }
};

// Get currently typing users for a chat
const getTypingUsers = (chatId) => {
  return Array.from(typingUsers.get(chatId) || []);
};

// Get active users count
const getActiveUsersCount = () => {
  return activeUsers.size;
};

module.exports = {
  handleChatSocket,
  getTypingUsers,
  getActiveUsersCount,
  activeUsers
};
